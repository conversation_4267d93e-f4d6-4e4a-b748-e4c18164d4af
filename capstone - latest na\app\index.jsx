import { StyleSheet, Text, View, TouchableOpacity, SafeAreaView, Image } from 'react-native';

import { useRouter } from 'expo-router';

const Welcome = () => {
  const router = useRouter();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.logoContainer}>
        {/* Mentalease Logo */}
        <View style={styles.logoPlaceholder}>
          <Image
            source={require('../assets/logo mentalease.png')}
            style={styles.logoImage}
            resizeMode="contain"
          />
        </View>
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>Welcome</Text>
        <Text style={styles.subtitle}>to</Text>
        <Text style={styles.title}>Mentalease</Text>

        <Text style={styles.description}>
          Your mobile companion for emotional well-being. Meet <PERSON><PERSON>, your mental chatbot companion,
          and access easy appointments and assessments for your mental well-being journey.
        </Text>

        <Text style={styles.byText}>by Sanda Diagnostic Center</Text>
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.accountButton}
          onPress={() => router.push('/sign-in')}
        >
          <Text style={styles.accountButtonText}>I already have an account</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.startedButton}
          onPress={() => router.push('/consent')}
        >
          <Text style={styles.startedButtonText}>Let's get started</Text>
        </TouchableOpacity>
      </View>

      {/* Decorative elements */}
      <View style={[styles.circle, styles.circleTop]} />
      <View style={[styles.circle, styles.circleLeft]} />
      <View style={[styles.circle, styles.circleRight]} />
    </SafeAreaView>
  );
};

export default Welcome;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    position: 'relative',
    overflow: 'hidden',
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F5F9EE',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  logoImage: {
    width: 100,
    height: 100,
  },
  logoText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#6B9142',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 22,
    color: '#6B9142',
    textAlign: 'center',
    marginVertical: 5,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginTop: 20,
    lineHeight: 24,
  },
  byText: {
    fontSize: 14,
    color: '#666666',
    marginTop: 20,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: 30,
    marginBottom: 40,
  },
  accountButton: {
    backgroundColor: '#6B9142',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 15,
  },
  accountButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  startedButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    paddingVertical: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#6B9142',
  },
  startedButtonText: {
    color: '#6B9142',
    fontSize: 16,
    fontWeight: '600',
  },
  // Decorative circles
  circle: {
    position: 'absolute',
    borderRadius: 100,
  },
  circleTop: {
    width: 200,
    height: 200,
    backgroundColor: '#F5F9EE',
    top: -100,
    right: -50,
    zIndex: -1,
  },
  circleLeft: {
    width: 60,
    height: 60,
    backgroundColor: '#6B9142',
    bottom: '40%',
    left: 20,
    zIndex: -1,
  },
  circleRight: {
    width: 40,
    height: 40,
    backgroundColor: '#6B9142',
    top: '30%',
    right: 30,
    zIndex: -1,
  },
});