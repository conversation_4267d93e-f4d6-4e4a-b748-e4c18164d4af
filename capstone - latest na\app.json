{"expo": {"scheme": "mentalease", "name": "Mentalease", "slug": "mentalease", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/logo mentalease.png", "userInterfaceStyle": "light", "newArchEnabled": true, "androidStatusBar": {"barStyle": "dark-content", "backgroundColor": "transparent", "translucent": true}, "splash": {"image": "./assets/logo mentalease.png", "resizeMode": "contain", "backgroundColor": "#F5F9EE"}, "ios": {"supportsTablet": true, "infoPlist": {"UIViewControllerBasedStatusBarAppearance": true, "UIStatusBarStyle": "UIStatusBarStyleDarkContent"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/logo mentalease.png", "backgroundColor": "#F5F9EE"}, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/logo mentalease.png"}, "plugins": ["expo-router"]}}